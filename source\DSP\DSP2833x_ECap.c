// TI文件 版本: /main/2
// 检入日期: 2007年3月15日 16:54:36
//###########################################################################
//
// 文件名:   DSP2833x_ECap.c
//
// 标题:    DSP2833x eCAP增强型捕获模块初始化和支持函数
//
//###########################################################################
// TI发布版本: 2833x/2823x头文件 V1.32
// 发布日期: 2010年6月28日
// 版权声明:
// 版权所有 (C) 2009-2021 德州仪器公司 - http://www.ti.com/
//
// 在满足以下条件的前提下，允许以源代码和二进制形式重新分发和使用，
// 无论是否进行修改:
//
//   源代码的重新分发必须保留上述版权声明、
//   此条件列表和以下免责声明。
//
//   二进制形式的重新分发必须在随分发提供的
//   文档和/或其他材料中复制上述版权声明、
//   此条件列表和以下免责声明。
//
//   未经事先书面许可，不得使用德州仪器公司的名称
//   或其贡献者的名称来认可或推广从本软件衍生的产品。
//
// 本软件由版权持有者和贡献者"按原样"提供，
// 不提供任何明示或暗示的保证，包括但不限于
// 适销性和特定用途适用性的暗示保证。
// 在任何情况下，版权所有者或贡献者均不对任何直接、间接、
// 偶然、特殊、惩罚性或后果性损害承担责任
// （包括但不限于替代商品或服务的采购；使用、数据或利润的损失；
// 或业务中断），无论是由何种责任理论引起的，
// 无论是合同责任、严格责任还是侵权责任
// （包括疏忽或其他），即使已被告知此类损害的可能性。
//
//###########################################################################

//
// 包含的头文件
//
#include "DSP2833x_Device.h"     // DSP2833x设备头文件
#include "DSP2833x_Examples.h"   // DSP2833x示例头文件
#include "SC_define.h"           // 新增的自定义定义头文件
//
// InitECap函数 - 此函数将eCAP模块初始化到已知状态
//
/*void
InitECap(void)
{
    //
    // 初始化eCAP1/2/3模块
    //
}*/

/**
 * @brief eCAP模块初始化函数
 * @details 配置eCAP1-6模块工作在APWM模式或捕获模式，用于PWM输出或频率测量
 */
void InitECap(void)
{
    /*=== eCAP1模块配置 - 用于APWM输出 ===*/

    // 设置eCAP1工作在APWM模式，配置周期和比较寄存器
    ECap1Regs.ECCTL2.bit.CAP_APWM  = 1;             // 设置为APWM模式（辅助PWM模式）
    // 0: ECAP模块工作在捕获模式，此模式强制以下配置：
        //· 禁止通过CTR = PRD事件复位TSCTR计数器
        //· 禁止CAP1和CAP2寄存器的影子加载
        //· 允许用户使能CAP1-4寄存器加载
        //· CAPx/APWMx引脚作为捕获输入
    // 1: ECAP模块工作在APWM模式，此模式强制以下配置：
        //· 在CTR = PRD事件时复位TSCTR计数器（周期边界）
        //· 允许CAP1和CAP2寄存器的影子加载
        //· 禁止时间戳加载到CAP1-4寄存器
        //· CAPx/APWMx引脚作为APWM输出

    ECap1Regs.ECCTL2.bit.APWMPOL   = 0;              // 设置APWM输出极性为高电平有效
    // 0: 输出为高电平有效（比较值决定高电平持续时间，先高后低）
    // 1: 输出为低电平有效

    ECap1Regs.ECCTL2.bit.SYNCI_EN  = 0;              // 禁止同步输入功能
    // 0: 禁止同步输入选项
    // 1: 使能计数器(TSCTR)在SYNCI信号或软件强制事件时从CTRPHS寄存器加载

    ECap1Regs.ECCTL2.bit.SYNCO_SEL = 2;              // 禁止同步输出
    // 00: 选择同步输入事件作为同步输出信号（直通）
    // 01: 选择CTR = PRD事件作为同步输出信号
    // 10: 禁止同步输出信号
    // 11: 禁止同步输出信号

    ECap1Regs.ECEINT.all           = 0;              // 禁止所有eCAP中断
    // bit15-8      0:      保留位
    // bit7         0:      CTR=CMP比较中断禁止
    // bit6         0:      CTR=PRD周期中断禁止
    // bit5         0:      CTROVF计数器溢出中断禁止
    // bit4         0:      CEVT4事件4中断禁止
    // bit3         0:      CEVT3事件3中断禁止
    // bit2         0:      CEVT2事件2中断禁止
    // bit1         0:      CEVT1事件1中断禁止
    // bit0         0:      保留位

    ECap1Regs.CAP1 = APWM_PERIOD-1;                 // 设置PWM周期值（APWM_PERIOD-1个时钟周期）
    ECap1Regs.CAP2 = 0;                             // 设置比较值为0（占空比为0%）
    ECap1Regs.CTRPHS = 0;                           // 设置计数器相位值为0（无延时）

    /*=== eCAP2模块配置 - 用于APWM输出 ===*/

    ECap2Regs.ECCTL2.bit.CAP_APWM = 1;               // 设置为APWM模式
    // 0: ECAP模块工作在捕获模式，此模式强制以下配置：
        //· 禁止通过CTR = PRD事件复位TSCTR计数器
        //· 禁止CAP1和CAP2寄存器的影子加载
        //· 允许用户使能CAP1-4寄存器加载
        //· CAPx/APWMx引脚作为捕获输入
    // 1: ECAP模块工作在APWM模式，此模式强制以下配置：
        //· 在CTR = PRD事件时复位TSCTR计数器（周期边界）
        //· 允许CAP1和CAP2寄存器的影子加载
        //· 禁止时间戳加载到CAP1-4寄存器
        //· CAPx/APWMx引脚作为APWM输出

    ECap2Regs.ECCTL2.bit.APWMPOL = 0;                // 设置APWM输出极性为高电平有效
    // 0: 输出为高电平有效
    // 1: 输出为低电平有效

    ECap2Regs.ECCTL2.bit.SYNCI_EN = 0;               // 禁止同步输入功能
    // 0: 禁止同步输入选项
    // 1: 使能计数器(TSCTR)在SYNCI信号或软件强制事件时从CTRPHS寄存器加载

    ECap2Regs.ECCTL2.bit.SYNCO_SEL = 2;              // 禁止同步输出
    // 00: 选择同步输入事件作为同步输出信号（直通）
    // 01: 选择CTR = PRD事件作为同步输出信号
    // 10: 禁止同步输出信号
    // 11: 禁止同步输出信号

    ECap2Regs.ECEINT.all = 0;                        // 禁止所有eCAP中断
    // bit15-8      0:      保留位
    // bit7         0:      CTR=CMP比较中断禁止
    // bit6         0:      CTR=PRD周期中断禁止
    // bit5         0:      CTROVF计数器溢出中断禁止
    // bit4         0:      CEVT4事件4中断禁止
    // bit3         0:      CEVT3事件3中断禁止
    // bit2         0:      CEVT2事件2中断禁止
    // bit1         0:      CEVT1事件1中断禁止
    // bit0         0:      保留位

    ECap2Regs.CAP1 = APWM_PERIOD-1;                 // 设置PWM周期值
    ECap2Regs.CAP2 = 0;                             // 设置比较值为0（占空比为0%）
    ECap2Regs.CTRPHS = 0;                           // 设置计数器相位值为0（无延时）

    /*=== eCAP3模块配置 - 用于APWM输出（带同步功能）===*/

    // 在eCAP3上设置APWM模式，配置周期和比较寄存器
    ECap3Regs.ECCTL2.bit.CAP_APWM = 1;               // 设置为APWM模式
    // 0: ECAP模块工作在捕获模式，此模式强制以下配置：
        //· 禁止通过CTR = PRD事件复位TSCTR计数器
        //· 禁止CAP1和CAP2寄存器的影子加载
        //· 允许用户使能CAP1-4寄存器加载
        //· CAPx/APWMx引脚作为捕获输入
    // 1: ECAP模块工作在APWM模式，此模式强制以下配置：
        //· 在CTR = PRD事件时复位TSCTR计数器（周期边界）
        //· 允许CAP1和CAP2寄存器的影子加载
        //· 禁止时间戳加载到CAP1-4寄存器
        //· CAPx/APWMx引脚作为APWM输出

    ECap3Regs.ECCTL2.bit.APWMPOL = 0;                // 设置APWM输出极性为高电平有效
    // 0: 输出为高电平有效
    // 1: 输出为低电平有效

    ECap3Regs.ECCTL2.bit.SYNCI_EN = 1;               // 使能同步输入功能
    // 0: 禁止同步输入选项
    // 1: 使能计数器(TSCTR)在SYNCI信号或软件强制事件时从CTRPHS寄存器加载

    ECap3Regs.ECCTL2.bit.SYNCO_SEL = 0;              // 选择同步输入事件作为同步输出
    // 00: 选择同步输入事件作为同步输出信号（直通）
    // 01: 选择CTR = PRD事件作为同步输出信号
    // 10: 禁止同步输出信号
    // 11: 禁止同步输出信号

    ECap3Regs.ECEINT.all = 0;                        // 禁止所有eCAP中断
    // bit15-8      0:      保留位
    // bit7         0:      CTR=CMP比较中断禁止
    // bit6         0:      CTR=PRD周期中断禁止
    // bit5         0:      CTROVF计数器溢出中断禁止
    // bit4         0:      CEVT4事件4中断禁止
    // bit3         0:      CEVT3事件3中断禁止
    // bit2         0:      CEVT2事件2中断禁止
    // bit1         0:      CEVT1事件1中断禁止
    // bit0         0:      保留位

    ECap3Regs.CAP1 = APWM_PERIOD-1;                 // 设置PWM周期值
    ECap3Regs.CAP2 = 0;                             // 设置比较值为0（占空比为0%）
    ECap3Regs.CTRPHS = 4680;                        // 设置计数器相位值为4680（相位延时）

    /*=== eCAP4模块配置 - 用于APWM输出（带同步功能）===*/

    // 在eCAP4上设置APWM模式，配置周期和比较寄存器
    ECap4Regs.ECCTL2.bit.CAP_APWM = 1;               // 设置为APWM模式
    // 0: ECAP模块工作在捕获模式，此模式强制以下配置：
        //· 禁止通过CTR = PRD事件复位TSCTR计数器
        //· 禁止CAP1和CAP2寄存器的影子加载
        //· 允许用户使能CAP1-4寄存器加载
        //· CAPx/APWMx引脚作为捕获输入
    // 1: ECAP模块工作在APWM模式，此模式强制以下配置：
        //· 在CTR = PRD事件时复位TSCTR计数器（周期边界）
        //· 允许CAP1和CAP2寄存器的影子加载
        //· 禁止时间戳加载到CAP1-4寄存器
        //· CAPx/APWMx引脚作为APWM输出

    ECap4Regs.ECCTL2.bit.APWMPOL = 0;                // 设置APWM输出极性为高电平有效
    // 0: 输出为高电平有效
    // 1: 输出为低电平有效

    ECap4Regs.ECCTL2.bit.SYNCI_EN = 1;               // 使能同步输入功能
    // 0: 禁止同步输入选项
    // 1: 使能计数器(TSCTR)在SYNCI信号或软件强制事件时从CTRPHS寄存器加载

    ECap4Regs.ECCTL2.bit.SYNCO_SEL = 0;              // 选择同步输入事件作为同步输出
    // 00: 选择同步输入事件作为同步输出信号（直通）
    // 01: 选择CTR = PRD事件作为同步输出信号
    // 10: 禁止同步输出信号
    // 11: 禁止同步输出信号

    ECap4Regs.ECEINT.all = 0;                        // 禁止所有eCAP中断
    // bit15-8      0:      保留位
    // bit7         0:      CTR=CMP比较中断禁止
    // bit6         0:      CTR=PRD周期中断禁止
    // bit5         0:      CTROVF计数器溢出中断禁止
    // bit4         0:      CEVT4事件4中断禁止
    // bit3         0:      CEVT3事件3中断禁止
    // bit2         0:      CEVT2事件2中断禁止
    // bit1         0:      CEVT1事件1中断禁止
    // bit0         0:      保留位

    ECap4Regs.CAP1 = APWM_PERIOD-1;                 // 设置PWM周期值
    ECap4Regs.CAP2 = 0;                             // 设置比较值为0（占空比为0%）
    ECap4Regs.CTRPHS = 1875;                        // 设置计数器相位值为1875（相位延时）

    /*=== 启动所有eCAP计数器 ===*/
    // 使能计数器开始计数
    ECap1Regs.ECCTL2.bit.TSCTRSTOP = 1;             // 启动eCAP1时间戳计数器
    ECap2Regs.ECCTL2.bit.TSCTRSTOP = 1;             // 启动eCAP2时间戳计数器
    ECap3Regs.ECCTL2.bit.TSCTRSTOP = 1;             // 启动eCAP3时间戳计数器
    ECap4Regs.ECCTL2.bit.TSCTRSTOP = 1;             // 启动eCAP4时间戳计数器

    /*=== eCAP5模块配置 - 用于频率测量 ===*/
    // 配置eCAP5单元用于频率测试
    //GpioCtrlRegs.GPBPUD.bit.GPIO48 = 0;    // 使能GPIO48上拉电阻 (CAP5)
    //GpioCtrlRegs.GPBQSEL2.bit.GPIO48 = 0; // GPIO48同步到SYSCLKOUT (CAP5)

    ECap5Regs.ECEINT.all = 0x0000;                  // 禁止所有eCAP中断
    ECap5Regs.ECCLR.all = 0xFFFF;                   // 清除所有CAP中断标志
    ECap5Regs.ECCTL1.bit.CAPLDEN = 0;               // 禁止捕获结果加载
    ECap5Regs.ECCTL2.bit.TSCTRSTOP = 0;             // 停止计数器

    ECap5Regs.ECCTL2.bit.CONT_ONESHT = 0;           // 设置为连续模式（0-连续模式）
    ECap5Regs.ECCTL2.bit.STOP_WRAP   = 0;           // 在连续模式下，捕获事件1后回绕
    ECap5Regs.ECCTL1.bit.CAP1POL     = 1;           // 捕获事件1触发极性设置
                                                    // 0-上升沿触发捕获事件1 (RE)
                                                    // 1-下降沿触发捕获事件1 (FE)
    ECap5Regs.ECCTL1.bit.CAP2POL     = 0;           // 捕获事件2上升沿触发
    ECap5Regs.ECCTL1.bit.CAP3POL     = 0;           // 捕获事件3上升沿触发
    ECap5Regs.ECCTL1.bit.CAP4POL     = 0;           // 捕获事件4上升沿触发
    ECap5Regs.ECCTL1.bit.CTRRST1     = 1;           // 事件1时间戳捕获后复位计数器（用于差分模式操作）
    ECap5Regs.ECCTL1.bit.CTRRST2     = 1;           // 事件2时间戳捕获后复位计数器
    ECap5Regs.ECCTL1.bit.CTRRST3     = 1;           // 事件3时间戳捕获后复位计数器
    ECap5Regs.ECCTL1.bit.CTRRST4     = 1;           // 事件4时间戳捕获后复位计数器
    ECap5Regs.ECCTL2.bit.SYNCI_EN    = 0;           // 禁止同步输入选项
    ECap5Regs.ECCTL2.bit.SYNCO_SEL   = 2;           // 禁止同步输出信号
    ECap5Regs.ECCTL1.bit.PRESCALE    = 0;           // 事件滤波器预分频：0-1分频
    ECap5Regs.ECCTL2.bit.TSCTRSTOP   = 1;           // 时间戳(TSCTR)计数器停止控制：1-自由运行
    ECap5Regs.ECCTL2.bit.REARM       = 1;           // 重新装载单次序列，执行以下操作：
                                                    // 1) 将Mod4计数器复位为零
                                                    // 2) 解冻Mod4计数器
                                                    // 3) 使能捕获寄存器加载
    ECap5Regs.ECCTL1.bit.CAPLDEN     = 1;           // 在捕获事件时使能CAP1-4寄存器加载
    ECap5Regs.ECEINT.bit.CEVT1       = 1;           // 使能捕获事件1作为中断源

    /*=== eCAP6模块配置 - 用于测试S相电网频率 ===*/
    // 配置eCAP6单元用于测试S相电网频率
    ECap6Regs.ECEINT.all             = 0x0000;      // 禁止所有eCAP中断
    ECap6Regs.ECCLR.all              = 0xFFFF;      // 清除所有CAP中断标志
    ECap6Regs.ECCTL1.bit.CAPLDEN     = 0;           // 禁止捕获结果加载
    ECap6Regs.ECCTL2.bit.TSCTRSTOP   = 0;           // 停止计数器

    ECap6Regs.ECCTL2.bit.CONT_ONESHT = 0;           // 设置为连续模式
    ECap6Regs.ECCTL2.bit.STOP_WRAP   = 0;           // 在连续模式下，捕获事件1后回绕
    ECap6Regs.ECCTL1.bit.CAP1POL     = 0;           // 捕获事件1上升沿触发
    ECap6Regs.ECCTL1.bit.CAP2POL     = 0;           // 捕获事件2上升沿触发
    ECap6Regs.ECCTL1.bit.CAP3POL     = 0;           // 捕获事件3上升沿触发
    ECap6Regs.ECCTL1.bit.CAP4POL     = 0;           // 捕获事件4上升沿触发
    ECap6Regs.ECCTL1.bit.CTRRST1     = 1;           // 事件1时间戳捕获后复位计数器
    ECap6Regs.ECCTL1.bit.CTRRST2     = 1;           // 事件2时间戳捕获后复位计数器
    ECap6Regs.ECCTL1.bit.CTRRST3     = 1;           // 事件3时间戳捕获后复位计数器
    ECap6Regs.ECCTL1.bit.CTRRST4     = 1;           // 事件4时间戳捕获后复位计数器
    ECap6Regs.ECCTL2.bit.SYNCI_EN    = 0;           // 禁止同步输入选项
    ECap6Regs.ECCTL2.bit.SYNCO_SEL   = 2;           // 禁止同步输出信号
    ECap6Regs.ECCTL1.bit.PRESCALE    = 0;           // 事件滤波器预分频：0-1分频
    ECap6Regs.ECCTL2.bit.TSCTRSTOP   = 1;           // 时间戳(TSCTR)计数器停止控制：1-自由运行
    ECap6Regs.ECCTL2.bit.REARM       = 1;           // 重新装载单次序列
    ECap6Regs.ECCTL1.bit.CAPLDEN     = 1;           // 在捕获事件时使能CAP1-4寄存器加载
    ECap6Regs.ECEINT.bit.CEVT1       = 1;           // 使能捕获事件1作为中断源

    /*=== 中断配置 ===*/
    PieCtrlRegs.PIEIER4.bit.INTx5    = 1;           // 在PIE组4中使能ECAP5中断
    PieCtrlRegs.PIEIER4.bit.INTx6    = 1;           // 在PIE组4中使能ECAP6中断
    IER |= M_INT4;                                  // 在IER中使能INT4以启用PIE组4
}
//
// InitECapGpio函数 - 此函数初始化GPIO引脚以用作ECAP引脚
//
// 每个GPIO引脚可以配置为GPIO引脚或最多3个不同的
// 外设功能引脚。默认情况下，复位后所有引脚都作为GPIO输入。
//
// 注意事项:
// 对于每个eCAP外设，只应使能一个GPIO引脚用于ECAP操作。
// 请注释掉其他不需要的行。
//

/**
 * @brief eCAP GPIO初始化主函数
 * @details 根据编译条件初始化各个eCAP模块对应的GPIO引脚
 */
void
InitECapGpio()
{
    InitECap1Gpio();                                // 初始化eCAP1的GPIO引脚
#if (DSP28_ECAP2)
    InitECap2Gpio();                                // 初始化eCAP2的GPIO引脚（如果支持）
#endif // endif DSP28_ECAP2
#if (DSP28_ECAP3)
    InitECap3Gpio();                                // 初始化eCAP3的GPIO引脚（如果支持）
#endif // endif DSP28_ECAP3
#if (DSP28_ECAP4)
    InitECap4Gpio();                                // 初始化eCAP4的GPIO引脚（如果支持）
#endif // endif DSP28_ECAP4
#if (DSP28_ECAP5)
    InitECap5Gpio();                                // 初始化eCAP5的GPIO引脚（如果支持）
#endif // endif DSP28_ECAP5
#if (DSP28_ECAP6)
    InitECap6Gpio();                                // 初始化eCAP6的GPIO引脚（如果支持）
#endif // endif DSP28_ECAP6
}

//
// InitECap1Gpio函数 - 初始化eCAP1的GPIO引脚配置
//

/**
 * @brief eCAP1 GPIO引脚初始化函数
 * @details 配置GPIO24作为eCAP1功能引脚，设置上拉电阻和同步选项
 */
void
InitECap1Gpio(void)
{
    EALLOW;                                         // 允许访问受保护的寄存器

    //
    // 为选定的引脚使能内部上拉电阻
    // 上拉电阻可以由用户使能或禁止。
    // 这将为指定的引脚使能上拉电阻。
    // 请注释掉其他不需要的行。
    //
    //GpioCtrlRegs.GPAPUD.bit.GPIO5 = 0;    // 使能GPIO5上拉电阻 (CAP1)
    GpioCtrlRegs.GPAPUD.bit.GPIO24 = 0;             // 使能GPIO24上拉电阻 (CAP1)
    //GpioCtrlRegs.GPBPUD.bit.GPIO34 = 0;   // 使能GPIO34上拉电阻 (CAP1)

    //
    // 输入默认同步到SYSCLKOUT。
    // 请注释掉其他不需要的行。
    //
    //GpioCtrlRegs.GPAQSEL1.bit.GPIO5 = 0;   // GPIO5同步到SYSCLKOUT (CAP1)
    GpioCtrlRegs.GPAQSEL2.bit.GPIO24 = 0;           // GPIO24同步到SYSCLKOUT (CAP1)
    //GpioCtrlRegs.GPBQSEL1.bit.GPIO34 = 0;  // GPIO34同步到SYSCLKOUT (CAP1)

    //
    // 使用GPIO寄存器配置eCAP-1引脚
    // 这指定了哪些可能的GPIO引脚将作为eCAP1功能引脚。
    // 请注释掉其他不需要的行。
    //
    //GpioCtrlRegs.GPAMUX1.bit.GPIO5 = 3;    // 配置GPIO5作为CAP1
    GpioCtrlRegs.GPAMUX2.bit.GPIO24 = 1;            // 配置GPIO24作为CAP1
    //GpioCtrlRegs.GPBMUX1.bit.GPIO34 = 1;   // 配置GPIO34作为CAP1

    EDIS;                                           // 禁止访问受保护的寄存器
}

#if DSP28_ECAP2
//
// InitECap2Gpio函数 - 初始化eCAP2的GPIO引脚配置
//

/**
 * @brief eCAP2 GPIO引脚初始化函数
 * @details 配置GPIO7作为eCAP2功能引脚，设置上拉电阻和同步选项
 */
void
InitECap2Gpio(void)
{
    EALLOW;                                         // 允许访问受保护的寄存器

    //
    // 为选定的引脚使能内部上拉电阻
    // 上拉电阻可以由用户使能或禁止。
    // 这将为指定的引脚使能上拉电阻。
    // 请注释掉其他不需要的行。
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO7 = 0;              // 使能GPIO7上拉电阻 (CAP2)
    //GpioCtrlRegs.GPAPUD.bit.GPIO25 = 0;    // 使能GPIO25上拉电阻 (CAP2)
    //GpioCtrlRegs.GPBPUD.bit.GPIO37 = 0;    // 使能GPIO37上拉电阻 (CAP2)

    //
    // 输入默认同步到SYSCLKOUT。
    // 请注释掉其他不需要的行。
    //
    GpioCtrlRegs.GPAQSEL1.bit.GPIO7 = 0;            // GPIO7同步到SYSCLKOUT (CAP2)
    //GpioCtrlRegs.GPAQSEL2.bit.GPIO25 = 0;  // GPIO25同步到SYSCLKOUT (CAP2)
    //GpioCtrlRegs.GPBQSEL1.bit.GPIO37 = 0;  // GPIO37同步到SYSCLKOUT (CAP2)

    //
    // 使用GPIO寄存器配置eCAP-2引脚
    // 这指定了哪些可能的GPIO引脚将作为eCAP2功能引脚。
    // 请注释掉其他不需要的行。
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO7 = 3;             // 配置GPIO7作为CAP2
    //GpioCtrlRegs.GPAMUX2.bit.GPIO25 = 1;   // 配置GPIO25作为CAP2
    //GpioCtrlRegs.GPBMUX1.bit.GPIO37 = 3;   // 配置GPIO37作为CAP2

    EDIS;                                           // 禁止访问受保护的寄存器
}
#endif // endif DSP28_ECAP2

#if DSP28_ECAP3
//
// InitECap3Gpio函数 - 初始化eCAP3的GPIO引脚配置
//

/**
 * @brief eCAP3 GPIO引脚初始化函数
 * @details 配置GPIO9作为eCAP3功能引脚，设置上拉电阻和同步选项
 */
void
InitECap3Gpio(void)
{
   EALLOW;                                          // 允许访问受保护的寄存器

/* 为选定的引脚使能内部上拉电阻 */
// 上拉电阻可以由用户使能或禁止。
// 这将为指定的引脚使能上拉电阻。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAPUD.bit.GPIO9 = 0;               // 使能GPIO9上拉电阻 (CAP3)
// GpioCtrlRegs.GPAPUD.bit.GPIO26 = 0;     // 使能GPIO26上拉电阻 (CAP3)

// 输入默认同步到SYSCLKOUT。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAQSEL1.bit.GPIO9 = 0;             // GPIO9同步到SYSCLKOUT (CAP3)
// GpioCtrlRegs.GPAQSEL2.bit.GPIO26 = 0;   // GPIO26同步到SYSCLKOUT (CAP3)

/* 使用GPIO寄存器配置eCAP-3引脚*/
// 这指定了哪些可能的GPIO引脚将作为eCAP3功能引脚。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAMUX1.bit.GPIO9 = 3;              // 配置GPIO9作为CAP3
// GpioCtrlRegs.GPAMUX2.bit.GPIO26 = 1;    // 配置GPIO26作为CAP3

    EDIS;                                           // 禁止访问受保护的寄存器
}
#endif // endif DSP28_ECAP3


#if DSP28_ECAP4
/**
 * @brief eCAP4 GPIO引脚初始化函数
 * @details 配置GPIO11作为eCAP4功能引脚，设置上拉电阻和同步选项
 */
void InitECap4Gpio(void)
{
   EALLOW;                                          // 允许访问受保护的寄存器

/* 为选定的引脚使能内部上拉电阻 */
// 上拉电阻可以由用户使能或禁止。
// 这将为指定的引脚使能上拉电阻。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAPUD.bit.GPIO11 = 0;              // 使能GPIO11上拉电阻 (CAP4)
// GpioCtrlRegs.GPAPUD.bit.GPIO27 = 0;   // 使能GPIO27上拉电阻 (CAP4)

// 输入默认同步到SYSCLKOUT。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAQSEL1.bit.GPIO11 = 0;            // GPIO11同步到SYSCLKOUT (CAP4)
// GpioCtrlRegs.GPAQSEL2.bit.GPIO27 = 0; // GPIO27同步到SYSCLKOUT (CAP4)

/* 使用GPIO寄存器配置eCAP-4引脚*/
// 这指定了哪些可能的GPIO引脚将作为eCAP4功能引脚。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAMUX1.bit.GPIO11 = 3;             // 配置GPIO11作为CAP4
// GpioCtrlRegs.GPAMUX2.bit.GPIO27 = 1;  // 配置GPIO27作为CAP4

    EDIS;                                           // 禁止访问受保护的寄存器
}
#endif // endif DSP28_ECAP4


#if DSP28_ECAP5
/**
 * @brief eCAP5 GPIO引脚初始化函数
 * @details 配置GPIO3作为eCAP5功能引脚，设置上拉电阻和同步选项
 */
void InitECap5Gpio(void)
{
   EALLOW;                                          // 允许访问受保护的寄存器

/* 为选定的引脚使能内部上拉电阻 */
// 上拉电阻可以由用户使能或禁止。
// 这将为指定的引脚使能上拉电阻。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAPUD.bit.GPIO3 = 0;               // 使能GPIO3上拉电阻 (CAP5)
// GpioCtrlRegs.GPBPUD.bit.GPIO48 = 0;    // 使能GPIO48上拉电阻 (CAP5)

// 输入默认同步到SYSCLKOUT。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAQSEL1.bit.GPIO3 = 0;             // GPIO3同步到SYSCLKOUT (CAP5)
// GpioCtrlRegs.GPBQSEL2.bit.GPIO48 = 0; // GPIO48同步到SYSCLKOUT (CAP5)

/* 使用GPIO寄存器配置eCAP-5引脚*/
// 这指定了哪些可能的GPIO引脚将作为eCAP5功能引脚。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAMUX1.bit.GPIO3 = 2;              // 配置GPIO3作为CAP5
// GpioCtrlRegs.GPBMUX2.bit.GPIO48 = 1;  // 配置GPIO48作为CAP5

    EDIS;                                           // 禁止访问受保护的寄存器
}
#endif // endif DSP28_ECAP5


#if DSP28_ECAP6
/**
 * @brief eCAP6 GPIO引脚初始化函数
 * @details 配置GPIO1作为eCAP6功能引脚，设置上拉电阻和同步选项
 */
void InitECap6Gpio(void)
{
   EALLOW;                                          // 允许访问受保护的寄存器

/* 为选定的引脚使能内部上拉电阻 */
// 上拉电阻可以由用户使能或禁止。
// 这将为指定的引脚使能上拉电阻。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAPUD.bit.GPIO1 = 0;               // 使能GPIO1上拉电阻 (CAP6)
// GpioCtrlRegs.GPBPUD.bit.GPIO49 = 0;    // 使能GPIO49上拉电阻 (CAP6)

// 输入默认同步到SYSCLKOUT。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAQSEL1.bit.GPIO1 = 0;             // GPIO1同步到SYSCLKOUT (CAP6)
// GpioCtrlRegs.GPBQSEL2.bit.GPIO49 = 0; // GPIO49同步到SYSCLKOUT (CAP6)

/* 使用GPIO寄存器配置eCAP-6引脚*/
// 这指定了哪些可能的GPIO引脚将作为eCAP6功能引脚。
// 请注释掉其他不需要的行。

   GpioCtrlRegs.GPAMUX1.bit.GPIO1 = 2;              // 配置GPIO1作为CAP6
// GpioCtrlRegs.GPBMUX2.bit.GPIO49 = 1;  // 配置GPIO49作为CAP6

    EDIS;                                           // 禁止访问受保护的寄存器
}
#endif // endif DSP28_ECAP6



//===========================================================================
// 文件结束
//===========================================================================
